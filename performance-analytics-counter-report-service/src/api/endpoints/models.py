# Copyright (c) 2021, Syskron GmbH. All rights reserved.

from typing import Any
from pydantic import BaseModel, ConfigDict, Field, field_validator
from pydantic.alias_generators import to_camel


# Use camel case for request response to adhere to OpenAPI specifications
# But keep snake case to adhere to PEP8
class ApiModel(BaseModel):
    """Base model for API responses with consistent configuration."""
    model_config = ConfigDict(
        alias_generator=to_camel,
        populate_by_name=True,
        from_attributes=True,
        extra="allow"  # Allow extra fields for backward compatibility
    )


class CounterReportResponse(ApiModel):
    """Response model for counter report endpoint."""
    customer: str = Field(..., description="Customer account identifier")
    eq_id: str = Field(..., alias="eqId", description="Equipment ID (machine or line ID)")
    time_from: int = Field(..., alias="timeFrom", description="Start timestamp in milliseconds")
    time_to: int = Field(..., alias="timeTo", description="End timestamp in milliseconds")
    units_produced: int = Field(default=0, alias="unitsProduced", description="Number of units produced")
    units_defect: int = Field(default=0, alias="unitsDefect", description="Number of defective units")
    units_total: int = Field(default=0, alias="unitsTotal", description="Total number of units")
    station_counters: dict[str, Any] | None = Field(
        default=None, alias="stationCounters", description="Station-specific counter data"
    )

    class Config:
        """Legacy config for backward compatibility."""
        validate_assignment = False

    @field_validator("units_produced", "units_defect", "units_total", mode="before")
    @classmethod
    def ensure_non_negative_units(cls, value: Any) -> int:  # noqa: N805
        """Ensure unit counts are non-negative integers."""
        if value is None:
            return 0
        if isinstance(value, str):
            try:
                value = int(value)
            except ValueError:
                return 0
        if isinstance(value, (int, float)):
            return max(0, int(value))
        return 0

    @field_validator("time_from", "time_to", mode="before")
    @classmethod
    def ensure_valid_timestamp(cls, value: Any) -> int:  # noqa: N805
        """Ensure timestamps are valid positive integers."""
        if isinstance(value, str):
            try:
                value = int(value)
            except ValueError as exc:
                raise ValueError(f"Invalid timestamp format: {value}") from exc
        if not isinstance(value, int) or value < 0:
            raise ValueError("Timestamp must be a non-negative integer")
        return value

    @field_validator("station_counters", mode="before")
    @classmethod
    def ensure_station_counters_is_dict(cls, value: Any) -> dict[str, Any] | None:  # noqa: N805
        """Ensure station_counters is a valid dictionary or None."""
        if value is None:
            return None
        if not isinstance(value, dict):
            return {}
        return value
